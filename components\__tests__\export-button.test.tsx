import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ExportButton } from '../ui/export-button';

// Mock the API calls
jest.mock('../../lib/api', () => ({
  exportData: jest.fn(),
}));

// Mock the download functionality
const mockDownload = jest.fn();
Object.defineProperty(window, 'URL', {
  value: {
    createObjectURL: jest.fn(() => 'mock-url'),
    revokeObjectURL: jest.fn(),
  },
});

// Mock the toast notifications
jest.mock('../../hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

describe('ExportButton Component', () => {
  const mockProps = {
    data: [
      { id: 1, name: 'Test Item 1', value: 100 },
      { id: 2, name: 'Test Item 2', value: 200 },
    ],
    filename: 'test-export',
    module: 'diligence-navigator',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders export button correctly', () => {
    render(<ExportButton {...mockProps} />);
    
    const exportButton = screen.getByRole('button', { name: /export/i });
    expect(exportButton).toBeInTheDocument();
    expect(exportButton).toHaveAttribute('data-testid', 'export-button');
  });

  it('opens export modal when clicked', async () => {
    const user = userEvent.setup();
    render(<ExportButton {...mockProps} />);
    
    const exportButton = screen.getByRole('button', { name: /export/i });
    await user.click(exportButton);
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Export Options')).toBeInTheDocument();
  });

  it('displays all export format options', async () => {
    const user = userEvent.setup();
    render(<ExportButton {...mockProps} />);
    
    await user.click(screen.getByRole('button', { name: /export/i }));
    
    expect(screen.getByLabelText(/format/i)).toBeInTheDocument();
    expect(screen.getByRole('option', { name: /pdf/i })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: /excel/i })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: /csv/i })).toBeInTheDocument();
  });

  it('handles PDF export correctly', async () => {
    const user = userEvent.setup();
    const { exportData } = require('../../lib/api');
    exportData.mockResolvedValue({
      blob: new Blob(['mock pdf content'], { type: 'application/pdf' }),
      filename: 'test-export.pdf',
    });

    render(<ExportButton {...mockProps} />);

    await user.click(screen.getByRole('button', { name: /export/i }));
    await user.selectOptions(screen.getByLabelText(/format/i), 'pdf');
    await user.click(screen.getByRole('button', { name: /export/i }));

    await waitFor(() => {
      expect(exportData).toHaveBeenCalledWith({
        data: mockProps.data,
        format: 'pdf',
        filename: 'test-export',
        module: 'diligence-navigator',
        options: expect.any(Object),
      });
    });
  });

  it('handles Excel export correctly', async () => {
    const user = userEvent.setup();
    const { exportData } = require('../../lib/api');
    exportData.mockResolvedValue({
      blob: new Blob(['mock excel content'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }),
      filename: 'test-export.xlsx',
    });

    render(<ExportButton {...mockProps} />);

    await user.click(screen.getByRole('button', { name: /export/i }));
    await user.selectOptions(screen.getByLabelText(/format/i), 'excel');
    await user.click(screen.getByRole('button', { name: /export/i }));

    await waitFor(() => {
      expect(exportData).toHaveBeenCalledWith({
        data: mockProps.data,
        format: 'excel',
        filename: 'test-export',
        module: 'diligence-navigator',
        options: expect.any(Object),
      });
    });
  });

  it('shows loading state during export', async () => {
    const user = userEvent.setup();
    const { exportData } = require('@/lib/api');
    exportData.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));

    render(<ExportButton {...mockProps} />);
    
    await user.click(screen.getByRole('button', { name: /export/i }));
    await user.selectOptions(screen.getByLabelText(/format/i), 'pdf');
    await user.click(screen.getByRole('button', { name: /export/i }));
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /exporting/i })).toBeDisabled();
  });

  it('handles export errors gracefully', async () => {
    const user = userEvent.setup();
    const { exportData } = require('@/lib/api');
    const { useToast } = require('@/hooks/use-toast');
    const mockToast = jest.fn();
    useToast.mockReturnValue({ toast: mockToast });
    
    exportData.mockRejectedValue(new Error('Export failed'));

    render(<ExportButton {...mockProps} />);
    
    await user.click(screen.getByRole('button', { name: /export/i }));
    await user.selectOptions(screen.getByLabelText(/format/i), 'pdf');
    await user.click(screen.getByRole('button', { name: /export/i }));
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Export Failed',
        description: 'There was an error exporting your data. Please try again.',
        variant: 'destructive',
      });
    });
  });

  it('includes selected options in export', async () => {
    const user = userEvent.setup();
    const { exportData } = require('@/lib/api');
    exportData.mockResolvedValue({
      blob: new Blob(['mock content']),
      filename: 'test-export.pdf',
    });

    render(<ExportButton {...mockProps} />);
    
    await user.click(screen.getByRole('button', { name: /export/i }));
    await user.selectOptions(screen.getByLabelText(/format/i), 'pdf');
    
    // Select additional options
    await user.check(screen.getByLabelText(/include headers/i));
    await user.check(screen.getByLabelText(/include summary/i));
    
    await user.click(screen.getByRole('button', { name: /export/i }));
    
    await waitFor(() => {
      expect(exportData).toHaveBeenCalledWith({
        data: mockProps.data,
        format: 'pdf',
        filename: 'test-export',
        module: 'diligence-navigator',
        options: {
          includeHeaders: true,
          includeSummary: true,
        },
      });
    });
  });

  it('validates required fields before export', async () => {
    const user = userEvent.setup();
    render(<ExportButton {...mockProps} />);
    
    await user.click(screen.getByRole('button', { name: /export/i }));
    // Don't select a format
    await user.click(screen.getByRole('button', { name: /export/i }));
    
    expect(screen.getByText(/please select a format/i)).toBeInTheDocument();
  });

  it('closes modal when cancelled', async () => {
    const user = userEvent.setup();
    render(<ExportButton {...mockProps} />);
    
    await user.click(screen.getByRole('button', { name: /export/i }));
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    
    await user.click(screen.getByRole('button', { name: /cancel/i }));
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('handles keyboard navigation correctly', async () => {
    render(<ExportButton {...mockProps} />);
    
    const exportButton = screen.getByRole('button', { name: /export/i });
    exportButton.focus();
    
    fireEvent.keyDown(exportButton, { key: 'Enter' });
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    
    fireEvent.keyDown(document, { key: 'Escape' });
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('is accessible with proper ARIA labels', async () => {
    const user = userEvent.setup();
    render(<ExportButton {...mockProps} />);
    
    const exportButton = screen.getByRole('button', { name: /export/i });
    expect(exportButton).toHaveAttribute('aria-label', 'Export data');
    
    await user.click(exportButton);
    
    const modal = screen.getByRole('dialog');
    expect(modal).toHaveAttribute('aria-labelledby');
    expect(modal).toHaveAttribute('aria-describedby');
  });

  it('supports custom export options based on module', () => {
    const customProps = {
      ...mockProps,
      module: 'valuation-hub',
    };
    
    render(<ExportButton {...customProps} />);
    // Module-specific options would be tested here
    // This is a placeholder for module-specific functionality
  });

  it('handles large datasets efficiently', async () => {
    const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
      id: i,
      name: `Item ${i}`,
      value: Math.random() * 1000,
    }));
    
    const user = userEvent.setup();
    const { exportData } = require('@/lib/api');
    exportData.mockResolvedValue({
      blob: new Blob(['mock content']),
      filename: 'large-export.csv',
    });

    render(<ExportButton {...mockProps} data={largeDataset} />);
    
    await user.click(screen.getByRole('button', { name: /export/i }));
    await user.selectOptions(screen.getByLabelText(/format/i), 'csv');
    await user.click(screen.getByRole('button', { name: /export/i }));
    
    await waitFor(() => {
      expect(exportData).toHaveBeenCalledWith({
        data: largeDataset,
        format: 'csv',
        filename: 'test-export',
        module: 'diligence-navigator',
        options: expect.any(Object),
      });
    });
  });
});
